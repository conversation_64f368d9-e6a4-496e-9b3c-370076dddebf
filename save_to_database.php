<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

try {
    // Get JSON input
    $input = file_get_contents('php://input');
    $data = json_decode($input, true);
    
    if (!$data) {
        throw new Exception('Invalid JSON data');
    }
    
    // Validate required fields
    $required_fields = ['originalFileName', 'uploadedFileName', 'bucket', 'size', 'uploadDate', 'downloadUrl', 'publicUrl'];
    foreach ($required_fields as $field) {
        if (!isset($data[$field])) {
            throw new Exception("Missing required field: $field");
        }
    }
    
    // CSV file path
    $csv_file = 'db_gcs_links.csv';
    
    // Check if file exists, if not create with headers
    if (!file_exists($csv_file)) {
        $headers = [
            'ID',
            'Original File Name',
            'Uploaded File Name',
            'Bucket',
            'File Size (Bytes)',
            'File Size (Formatted)',
            'Upload Date',
            'Download URL',
            'Public URL',
            'Status'
        ];
        
        $fp = fopen($csv_file, 'w');
        if (!$fp) {
            throw new Exception('Cannot create CSV file');
        }
        fputcsv($fp, $headers);
        fclose($fp);
    }
    
    // Format file size
    function formatFileSize($bytes) {
        if ($bytes == 0) return '0 Bytes';
        $k = 1024;
        $sizes = ['Bytes', 'KB', 'MB', 'GB'];
        $i = floor(log($bytes) / log($k));
        return round(($bytes / pow($k, $i)), 2) . ' ' . $sizes[$i];
    }
    
    // Prepare CSV row
    $csv_row = [
        time(), // ID (timestamp)
        $data['originalFileName'],
        $data['uploadedFileName'],
        $data['bucket'],
        $data['size'],
        formatFileSize($data['size']),
        $data['uploadDate'],
        $data['downloadUrl'],
        $data['publicUrl'],
        'Active'
    ];
    
    // Append to CSV file
    $fp = fopen($csv_file, 'a');
    if (!$fp) {
        throw new Exception('Cannot open CSV file for writing');
    }
    
    if (fputcsv($fp, $csv_row) === false) {
        fclose($fp);
        throw new Exception('Failed to write to CSV file');
    }
    
    fclose($fp);
    
    // Return success response
    echo json_encode([
        'success' => true,
        'message' => 'Record saved to database',
        'record' => $csv_row
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'error' => $e->getMessage()
    ]);
}
?>
