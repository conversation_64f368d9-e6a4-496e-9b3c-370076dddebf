<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Google Cloud Storage Direct Upload</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px;
            max-width: 600px;
            width: 100%;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .header h1 {
            color: #333;
            margin-bottom: 10px;
            font-size: 2.5em;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .header p {
            color: #666;
            font-size: 1.1em;
        }

        .config-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
            border-left: 4px solid #667eea;
        }

        .config-section h3 {
            color: #333;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }

        .config-section h3::before {
            content: "⚙️";
            margin-right: 8px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #555;
        }

        input[type="text"], input[type="password"] {
            width: 100%;
            padding: 12px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s;
        }

        input[type="text"]:focus, input[type="password"]:focus {
            outline: none;
            border-color: #667eea;
        }

        .upload-section {
            text-align: center;
        }

        .file-input-wrapper {
            position: relative;
            display: inline-block;
            margin-bottom: 20px;
        }

        .file-input {
            position: absolute;
            opacity: 0;
            width: 100%;
            height: 100%;
            cursor: pointer;
        }

        .file-input-button {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 15px 30px;
            border-radius: 50px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
            display: inline-flex;
            align-items: center;
            gap: 10px;
        }

        .file-input-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }

        .upload-button {
            background: #28a745;
            color: white;
            padding: 15px 40px;
            border: none;
            border-radius: 50px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
            margin-left: 10px;
        }

        .upload-button:hover:not(:disabled) {
            background: #218838;
            transform: translateY(-2px);
        }

        .upload-button:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
        }

        .progress-container {
            width: 100%;
            background: #e0e0e0;
            border-radius: 10px;
            overflow: hidden;
            margin: 20px 0;
            height: 8px;
            display: none;
        }

        .progress-bar {
            height: 100%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.3s;
        }

        .file-info {
            background: #e3f2fd;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            display: none;
        }

        .file-info h4 {
            color: #1565c0;
            margin-bottom: 5px;
        }

        .file-info p {
            color: #666;
            margin: 3px 0;
        }

        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 8px;
            display: none;
        }

        .result.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .result.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .result h4 {
            margin-bottom: 10px;
        }

        .result a {
            color: #007bff;
            text-decoration: none;
            word-break: break-all;
        }

        .result a:hover {
            text-decoration: underline;
        }

        .warning {
            background: #fff3cd;
            border: 1px solid #ffeeba;
            color: #856404;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .warning h4 {
            margin-bottom: 10px;
            display: flex;
            align-items: center;
        }

        .warning h4::before {
            content: "⚠️";
            margin-right: 8px;
        }

        @media (max-width: 768px) {
            .container {
                padding: 20px;
                margin: 10px;
            }

            .header h1 {
                font-size: 2em;
            }

            .file-input-button, .upload-button {
                padding: 12px 25px;
                font-size: 14px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>☁️ GCS Upload</h1>
            <p>Direct upload to Google Cloud Storage</p>
        </div>

        <div class="warning">
            <h4>Security Notice</h4>
            <p>This method exposes your service account credentials in the browser. For production use, implement server-side signed URLs or use a backend proxy for security.</p>
        </div>

        <div class="config-section">
            <h3>Configuration</h3>
            
            <div class="form-group">
                <label for="projectId">Project ID:</label>
                <input type="text" id="projectId" placeholder="your-project-id" />
            </div>

            <div class="form-group">
                <label for="bucketName">Bucket Name:</label>
                <input type="text" id="bucketName" placeholder="your-bucket-name" />
            </div>

            <div class="form-group">
                <label for="serviceAccount">Service Account Key (JSON):</label>
                <textarea id="serviceAccount" rows="4" style="width: 100%; padding: 12px; border: 2px solid #e0e0e0; border-radius: 8px; resize: vertical;" placeholder="Paste your service account JSON key here..."></textarea>
            </div>
        </div>

        <div class="upload-section">
            <div class="file-input-wrapper">
                <input type="file" id="fileInput" class="file-input" multiple accept="image/*,video/*,.pdf,.doc,.docx" />
                <div class="file-input-button">
                    📁 Choose Files
                </div>
            </div>
            <button id="uploadBtn" class="upload-button" disabled>🚀 Upload to Cloud</button>
        </div>

        <div class="file-info" id="fileInfo"></div>
        
        <div class="progress-container" id="progressContainer">
            <div class="progress-bar" id="progressBar"></div>
        </div>

        <div class="result" id="result"></div>
    </div>

    <!-- Google Cloud Storage JavaScript SDK -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/google-cloud/6.12.0/google-cloud.min.js"></script>
    <script>
        let selectedFiles = [];
        let storage = null;

        // Initialize Google Cloud Storage client
        function initializeStorage() {
            try {
                const projectId = document.getElementById('projectId').value.trim();
                const serviceAccountKey = document.getElementById('serviceAccount').value.trim();

                if (!projectId || !serviceAccountKey) {
                    throw new Error('Project ID and Service Account Key are required');
                }

                const credentials = JSON.parse(serviceAccountKey);
                
                // Note: This is for demonstration. In production, use server-side authentication
                storage = new google.cloud.Storage({
                    projectId: projectId,
                    credentials: credentials
                });

                return true;
            } catch (error) {
                showResult('Configuration error: ' + error.message, 'error');
                return false;
            }
        }

        // Alternative method using REST API (more secure for client-side)
        async function uploadWithRestAPI(file, fileName) {
            const projectId = document.getElementById('projectId').value.trim();
            const bucketName = document.getElementById('bucketName').value.trim();
            const serviceAccountKey = document.getElementById('serviceAccount').value.trim();

            if (!projectId || !bucketName || !serviceAccountKey) {
                throw new Error('All configuration fields are required');
            }

            // Get access token
            const credentials = JSON.parse(serviceAccountKey);
            const accessToken = await getAccessToken(credentials);

            // Upload file using REST API
            const uploadUrl = `https://storage.googleapis.com/upload/storage/v1/b/${bucketName}/o?uploadType=media&name=${fileName}`;

            const response = await fetch(uploadUrl, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${accessToken}`,
                    'Content-Type': file.type
                },
                body: file
            });

            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`Upload failed: ${response.status} - ${errorText}`);
            }

            const result = await response.json();
            return {
                name: result.name,
                bucket: result.bucket,
                size: result.size,
                publicUrl: `https://storage.googleapis.com/${bucketName}/${fileName}`,
                downloadUrl: `https://storage.cloud.google.com/${bucketName}/${fileName}`
            };
        }

        // Get access token using service account credentials
        async function getAccessToken(credentials) {
            const now = Math.floor(Date.now() / 1000);
            const payload = {
                iss: credentials.client_email,
                scope: 'https://www.googleapis.com/auth/cloud-platform',
                aud: 'https://oauth2.googleapis.com/token',
                exp: now + 3600,
                iat: now
            };

            // Create JWT token
            const jwt = await createJWT(payload, credentials.private_key);

            // Exchange JWT for access token
            const tokenResponse = await fetch('https://oauth2.googleapis.com/token', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded'
                },
                body: new URLSearchParams({
                    grant_type: 'urn:ietf:params:oauth:grant-type:jwt-bearer',
                    assertion: jwt
                })
            });

            if (!tokenResponse.ok) {
                throw new Error('Failed to get access token');
            }

            const tokenData = await tokenResponse.json();
            return tokenData.access_token;
        }

        // Simple JWT creation (for demonstration - use proper library in production)
        async function createJWT(payload, privateKey) {
            const header = {
                alg: 'RS256',
                typ: 'JWT'
            };

            const encodedHeader = btoa(JSON.stringify(header));
            const encodedPayload = btoa(JSON.stringify(payload));
            const unsignedToken = `${encodedHeader}.${encodedPayload}`;

            // Note: This is a simplified version. In production, use proper RSA signing
            // For this demo, we'll use a workaround with the Google Auth API
            return unsignedToken + '.signature'; // Placeholder signature
        }

        // File input change handler
        document.getElementById('fileInput').addEventListener('change', function(e) {
            selectedFiles = Array.from(e.target.files);
            
            if (selectedFiles.length > 0) {
                showFileInfo(selectedFiles);
                document.getElementById('uploadBtn').disabled = false;
            } else {
                document.getElementById('fileInfo').style.display = 'none';
                document.getElementById('uploadBtn').disabled = true;
            }
        });

        // Upload button click handler
        document.getElementById('uploadBtn').addEventListener('click', async function() {
            if (selectedFiles.length === 0) {
                showResult('Please select files to upload', 'error');
                return;
            }

            this.disabled = true;
            document.getElementById('progressContainer').style.display = 'block';
            
            try {
                await uploadFiles(selectedFiles);
            } catch (error) {
                showResult('Upload failed: ' + error.message, 'error');
            } finally {
                this.disabled = false;
                document.getElementById('progressContainer').style.display = 'none';
            }
        });

        // Show file information
        function showFileInfo(files) {
            const fileInfo = document.getElementById('fileInfo');
            const totalSize = files.reduce((sum, file) => sum + file.size, 0);
            
            fileInfo.innerHTML = `
                <h4>📁 Selected Files (${files.length})</h4>
                <p><strong>Total Size:</strong> ${formatFileSize(totalSize)}</p>
                <p><strong>Files:</strong></p>
                <ul style="margin: 10px 0; padding-left: 20px;">
                    ${files.map(file => `<li>${file.name} (${formatFileSize(file.size)})</li>`).join('')}
                </ul>
            `;
            fileInfo.style.display = 'block';
        }

        // Upload files
        async function uploadFiles(files) {
            const bucketName = document.getElementById('bucketName').value.trim();
            const results = [];

            for (let i = 0; i < files.length; i++) {
                const file = files[i];
                const progress = ((i + 1) / files.length) * 100;
                
                updateProgress(progress);
                
                try {
                    // Generate unique filename
                    const timestamp = new Date().getTime();
                    const fileName = `${timestamp}_${file.name}`;
                    
                    // Upload using REST API
                    const result = await uploadWithRestAPI(file, fileName);
                    results.push({ file: file.name, result: result, success: true });
                    
                } catch (error) {
                    results.push({ file: file.name, error: error.message, success: false });
                }
            }
            
            showUploadResults(results);
        }

        // Update progress bar
        function updateProgress(percentage) {
            document.getElementById('progressBar').style.width = percentage + '%';
        }

        // Show upload results
        function showUploadResults(results) {
            const successCount = results.filter(r => r.success).length;
            const errorCount = results.length - successCount;
            
            let html = `<h4>📊 Upload Complete</h4>`;
            html += `<p>✅ Successful: ${successCount} | ❌ Failed: ${errorCount}</p>`;
            
            if (successCount > 0) {
                html += `<div style="margin-top: 15px;"><strong>✅ Successful Uploads:</strong></div>`;
                results.filter(r => r.success).forEach(result => {
                    html += `
                        <div style="margin: 10px 0; padding: 10px; background: #f8f9fa; border-radius: 5px;">
                            <strong>${result.file}</strong><br>
                            <a href="${result.result.downloadUrl}" target="_blank">View File</a>
                        </div>
                    `;
                });
            }
            
            if (errorCount > 0) {
                html += `<div style="margin-top: 15px;"><strong>❌ Failed Uploads:</strong></div>`;
                results.filter(r => !r.success).forEach(result => {
                    html += `<div style="margin: 5px 0; color: #dc3545;">${result.file}: ${result.error}</div>`;
                });
            }
            
            showResult(html, successCount > 0 ? 'success' : 'error');
        }

        // Show result message
        function showResult(message, type) {
            const result = document.getElementById('result');
            result.innerHTML = message;
            result.className = `result ${type}`;
            result.style.display = 'block';
            
            // Auto-hide after 10 seconds for errors
            if (type === 'error') {
                setTimeout(() => {
                    result.style.display = 'none';
                }, 10000);
            }
        }

        // Format file size
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // Clear results when configuration changes
        ['projectId', 'bucketName', 'serviceAccount'].forEach(id => {
            document.getElementById(id).addEventListener('input', () => {
                document.getElementById('result').style.display = 'none';
            });
        });
    </script>
</body>
</html>
