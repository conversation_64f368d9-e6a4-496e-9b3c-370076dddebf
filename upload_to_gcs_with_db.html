<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Upload to Google Cloud Storage with Database</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4285f4 0%, #34a853 50%, #fbbc05 75%, #ea4335 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .content {
            padding: 40px;
        }

        .config-section {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            border-left: 5px solid #4285f4;
        }

        .config-section h3 {
            color: #333;
            margin-bottom: 20px;
            font-size: 1.3em;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #555;
        }

        .form-group input, .form-group textarea {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .form-group input:focus, .form-group textarea:focus {
            outline: none;
            border-color: #4285f4;
            box-shadow: 0 0 0 3px rgba(66, 133, 244, 0.1);
        }

        .form-group textarea {
            resize: vertical;
            min-height: 120px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }

        .upload-section {
            text-align: center;
            margin: 30px 0;
        }

        .file-input-container {
            position: relative;
            display: inline-block;
            margin-bottom: 20px;
        }

        .file-input {
            position: absolute;
            opacity: 0;
            width: 100%;
            height: 100%;
            cursor: pointer;
        }

        .file-input-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 30px;
            border-radius: 50px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-block;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }

        .file-input-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
        }

        .upload-button {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 50px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(76, 175, 80, 0.4);
            margin: 5px;
        }

        .upload-button:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(76, 175, 80, 0.6);
        }

        .upload-button:disabled {
            background: #cccccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .file-info {
            background: #e8f5e8;
            border: 2px solid #4CAF50;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            display: none;
        }

        .file-info h4 {
            color: #2e7d32;
            margin-bottom: 10px;
        }

        .progress-container {
            background: #f0f0f0;
            border-radius: 25px;
            padding: 5px;
            margin: 20px 0;
            display: none;
        }

        .progress-bar {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            height: 30px;
            border-radius: 20px;
            width: 0%;
            transition: width 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
        }

        .result {
            margin: 20px 0;
            padding: 20px;
            border-radius: 15px;
            display: none;
        }

        .result.success {
            background: #d4edda;
            border: 2px solid #28a745;
            color: #155724;
        }

        .result.error {
            background: #f8d7da;
            border: 2px solid #dc3545;
            color: #721c24;
        }

        .result h4 {
            margin-bottom: 15px;
            font-size: 1.2em;
        }

        .result ul {
            list-style: none;
            padding: 0;
        }

        .result li {
            background: rgba(255,255,255,0.7);
            margin: 10px 0;
            padding: 15px;
            border-radius: 10px;
            border-left: 4px solid #28a745;
        }

        .result li.error {
            border-left-color: #dc3545;
        }

        .result a {
            color: #007bff;
            text-decoration: none;
            font-weight: 600;
        }

        .result a:hover {
            text-decoration: underline;
        }

        .database-info {
            background: #e3f2fd;
            border: 2px solid #2196f3;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
        }

        .database-info h4 {
            color: #1565c0;
            margin-bottom: 10px;
        }

        .database-view {
            background: #f8f9fa;
            border: 2px solid #dee2e6;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            display: none;
            max-height: 500px;
            overflow-y: auto;
        }

        .database-view h4 {
            color: #495057;
            margin-bottom: 15px;
            border-bottom: 2px solid #dee2e6;
            padding-bottom: 10px;
        }

        .database-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
            font-size: 14px;
        }

        .database-table th {
            background: #007bff;
            color: white;
            padding: 12px 8px;
            text-align: left;
            font-weight: 600;
            border: 1px solid #0056b3;
        }

        .database-table td {
            padding: 10px 8px;
            border: 1px solid #dee2e6;
            vertical-align: top;
        }

        .database-table tr:nth-child(even) {
            background: #f8f9fa;
        }

        .database-table tr:hover {
            background: #e3f2fd;
        }

        .download-link {
            background: #28a745;
            color: white;
            padding: 5px 10px;
            border-radius: 5px;
            text-decoration: none;
            font-size: 12px;
            display: inline-block;
            margin: 2px;
        }

        .download-link:hover {
            background: #218838;
            color: white;
            text-decoration: none;
        }

        .public-link {
            background: #17a2b8;
            color: white;
            padding: 5px 10px;
            border-radius: 5px;
            text-decoration: none;
            font-size: 12px;
            display: inline-block;
            margin: 2px;
        }

        .public-link:hover {
            background: #138496;
            color: white;
            text-decoration: none;
        }

        .close-db-btn {
            background: #dc3545;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            float: right;
            margin-bottom: 10px;
        }

        .close-db-btn:hover {
            background: #c82333;
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 15px;
            }
            
            .content {
                padding: 20px;
            }
            
            .header {
                padding: 20px;
            }
            
            .header h1 {
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Google Cloud Storage Uploader</h1>
            <p>Upload files to Google Cloud Storage with automatic database logging</p>
        </div>

        <div class="content">
            <div class="config-section">
                <h3>⚙️ Configuration</h3>
                
                <div class="form-group">
                    <label for="projectId">Google Cloud Project ID:</label>
                    <input type="text" id="projectId" placeholder="your-project-id" value="gen-lang-client-**********">
                </div>

                <div class="form-group">
                    <label for="bucketName">Storage Bucket Name:</label>
                    <input type="text" id="bucketName" placeholder="your-bucket-name" value="dakoii_test_bucket-1">
                </div>

                <div class="form-group">
                    <label for="serviceAccount">Service Account Key (JSON):</label>
                    <textarea id="serviceAccount" placeholder="Paste your service account JSON key here..."></textarea>
                </div>
            </div>

            <div class="database-info">
                <h4>📊 Database Status</h4>
                <p>Backend database: <strong>db_gcs_links.csv</strong></p>
                <p id="dbStatus">Loading database status...</p>
            </div>

            <div class="upload-section">
                <div class="file-input-container">
                    <input type="file" id="fileInput" class="file-input" multiple>
                    <div class="file-input-button">
                        📁 Choose Files
                    </div>
                </div>
                <button id="uploadBtn" class="upload-button" disabled>🚀 Upload to Cloud</button>
                <button id="viewDbBtn" class="upload-button" style="background: #17a2b8;">📊 View Database</button>
            </div>

            <div class="file-info" id="fileInfo"></div>
            
            <div class="progress-container" id="progressContainer">
                <div class="progress-bar" id="progressBar">0%</div>
            </div>

            <div class="result" id="result"></div>

            <div class="database-view" id="databaseView">
                <button class="close-db-btn" onclick="closeDatabaseView()">✕ Close</button>
                <h4>📊 Database Records</h4>
                <div id="databaseContent">Loading database...</div>
            </div>
        </div>
    </div>

    <script>
        let selectedFiles = [];
        let storage = null;
        const CSV_DATABASE_PATH = 'db_gcs_links.csv'; // Backend database file

        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            loadDatabaseStatus();
        });

        // Load database status
        async function loadDatabaseStatus() {
            try {
                const response = await fetch(CSV_DATABASE_PATH);
                if (response.ok) {
                    const csvContent = await response.text();
                    const lines = csvContent.split('\n').filter(line => line.trim());
                    const recordCount = Math.max(0, lines.length - 1); // Subtract header
                    document.getElementById('dbStatus').innerHTML = `✅ Database loaded: <strong>${recordCount} records</strong>`;
                } else {
                    document.getElementById('dbStatus').innerHTML = `⚠️ Database file not found - will be created on first upload`;
                }
            } catch (error) {
                document.getElementById('dbStatus').innerHTML = `⚠️ Database file not found - will be created on first upload`;
            }
        }

        // File input change handler
        document.getElementById('fileInput').addEventListener('change', function(e) {
            selectedFiles = Array.from(e.target.files);
            if (selectedFiles.length > 0) {
                showFileInfo(selectedFiles);
                document.getElementById('uploadBtn').disabled = false;
            } else {
                document.getElementById('fileInfo').style.display = 'none';
                document.getElementById('uploadBtn').disabled = true;
            }
        });

        // Upload button click handler
        document.getElementById('uploadBtn').addEventListener('click', async function() {
            if (selectedFiles.length === 0) {
                showResult('Please select files to upload', 'error');
                return;
            }

            const projectId = document.getElementById('projectId').value.trim();
            const bucketName = document.getElementById('bucketName').value.trim();
            const serviceAccountJson = document.getElementById('serviceAccount').value.trim();

            if (!projectId || !bucketName || !serviceAccountJson) {
                showResult('Please fill in all configuration fields', 'error');
                return;
            }

            try {
                const serviceAccount = JSON.parse(serviceAccountJson);
                await uploadFiles(selectedFiles);
            } catch (error) {
                showResult(`Configuration error: ${error.message}`, 'error');
            }
        });

        // View database button click handler
        document.getElementById('viewDbBtn').addEventListener('click', function() {
            loadAndDisplayDatabase();
        });

        // Show file information
        function showFileInfo(files) {
            const fileInfo = document.getElementById('fileInfo');
            const totalSize = files.reduce((sum, file) => sum + file.size, 0);

            fileInfo.innerHTML = `
                <h4>📁 Selected Files (${files.length})</h4>
                <p><strong>Total Size:</strong> ${formatFileSize(totalSize)}</p>
                <p><strong>Files:</strong></p>
                <ul style="margin: 10px 0; padding-left: 20px;">
                    ${files.map(file => `<li>${file.name} (${formatFileSize(file.size)})</li>`).join('')}
                </ul>
            `;
            fileInfo.style.display = 'block';
        }

        // Upload files and save to database
        async function uploadFiles(files) {
            const bucketName = document.getElementById('bucketName').value.trim();
            const results = [];

            document.getElementById('progressContainer').style.display = 'block';

            for (let i = 0; i < files.length; i++) {
                const file = files[i];
                const progress = ((i + 1) / files.length) * 100;

                updateProgress(progress);

                try {
                    // Generate unique filename
                    const timestamp = new Date().getTime();
                    const fileName = `${timestamp}_${file.name}`;

                    // Upload using REST API
                    const result = await uploadWithRestAPI(file, fileName);
                    results.push({ file: file.name, result: result, success: true });

                    // Save to database
                    await saveToDatabase({
                        originalFileName: file.name,
                        uploadedFileName: result.name,
                        bucket: result.bucket,
                        size: result.size,
                        uploadDate: new Date().toISOString(),
                        downloadUrl: `https://storage.cloud.google.com/${bucketName}/${fileName}`,
                        publicUrl: `https://storage.googleapis.com/${bucketName}/${fileName}`
                    });

                } catch (error) {
                    results.push({ file: file.name, error: error.message, success: false });
                }
            }

            showUploadResults(results);
            loadDatabaseStatus(); // Refresh database status
        }

        // Save upload record to database
        async function saveToDatabase(record) {
            try {
                // Save to server-side CSV file via PHP script
                const response = await fetch('save_to_database.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(record)
                });

                if (!response.ok) {
                    throw new Error(`Server error: ${response.status}`);
                }

                const result = await response.json();

                if (result.success) {
                    console.log('✅ Record saved to CSV database:', result.record);
                } else {
                    throw new Error(result.error || 'Failed to save to database');
                }

                // Also save to localStorage as backup
                let existingData = localStorage.getItem('gcs_database') || '';

                // Add header if empty
                if (!existingData) {
                    const headers = [
                        'ID',
                        'Original File Name',
                        'Uploaded File Name',
                        'Bucket',
                        'File Size (Bytes)',
                        'File Size (Formatted)',
                        'Upload Date',
                        'Download URL',
                        'Public URL',
                        'Status'
                    ].join(',');
                    existingData = headers + '\n';
                }

                // Create CSV row for localStorage
                const csvRow = [
                    new Date().getTime(), // ID (timestamp)
                    `"${record.originalFileName}"`,
                    `"${record.uploadedFileName}"`,
                    `"${record.bucket}"`,
                    record.size,
                    `"${formatFileSize(record.size)}"`,
                    `"${record.uploadDate}"`,
                    `"${record.downloadUrl}"`,
                    `"${record.publicUrl}"`,
                    '"Active"'
                ].join(',');

                // Append new record to localStorage
                existingData += csvRow + '\n';
                localStorage.setItem('gcs_database', existingData);

            } catch (error) {
                console.error('❌ Error saving to database:', error);

                // If server-side fails, at least save to localStorage
                try {
                    let existingData = localStorage.getItem('gcs_database') || '';

                    if (!existingData) {
                        const headers = [
                            'ID',
                            'Original File Name',
                            'Uploaded File Name',
                            'Bucket',
                            'File Size (Bytes)',
                            'File Size (Formatted)',
                            'Upload Date',
                            'Download URL',
                            'Public URL',
                            'Status'
                        ].join(',');
                        existingData = headers + '\n';
                    }

                    const csvRow = [
                        new Date().getTime(),
                        `"${record.originalFileName}"`,
                        `"${record.uploadedFileName}"`,
                        `"${record.bucket}"`,
                        record.size,
                        `"${formatFileSize(record.size)}"`,
                        `"${record.uploadDate}"`,
                        `"${record.downloadUrl}"`,
                        `"${record.publicUrl}"`,
                        '"Active"'
                    ].join(',');

                    existingData += csvRow + '\n';
                    localStorage.setItem('gcs_database', existingData);
                    console.log('💾 Saved to localStorage as fallback');

                } catch (localError) {
                    console.error('Failed to save to localStorage:', localError);
                }
            }
        }

        // Upload file using REST API
        async function uploadWithRestAPI(file, fileName) {
            const bucketName = document.getElementById('bucketName').value.trim();
            const serviceAccountJson = document.getElementById('serviceAccount').value.trim();
            const serviceAccount = JSON.parse(serviceAccountJson);

            // Create JWT token
            const token = await createJWT(serviceAccount);

            // Upload file
            const uploadUrl = `https://storage.googleapis.com/upload/storage/v1/b/${bucketName}/o?uploadType=media&name=${encodeURIComponent(fileName)}`;

            console.log('Uploading to:', uploadUrl);

            const response = await fetch(uploadUrl, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': file.type || 'application/octet-stream'
                },
                body: file
            });

            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`Upload failed: ${response.status} ${response.statusText} - ${errorText}`);
            }

            const result = await response.json();
            return {
                name: result.name,
                bucket: result.bucket,
                size: result.size
            };
        }

        // Create JWT token for authentication
        async function createJWT(serviceAccount) {
            const header = {
                alg: 'RS256',
                typ: 'JWT'
            };

            const now = Math.floor(Date.now() / 1000);
            const payload = {
                iss: serviceAccount.client_email,
                scope: 'https://www.googleapis.com/auth/cloud-platform',
                aud: 'https://oauth2.googleapis.com/token',
                exp: now + 3600,
                iat: now
            };

            console.log('Creating JWT with payload:', payload);

            const encodedHeader = btoa(JSON.stringify(header)).replace(/=/g, '').replace(/\+/g, '-').replace(/\//g, '_');
            const encodedPayload = btoa(JSON.stringify(payload)).replace(/=/g, '').replace(/\+/g, '-').replace(/\//g, '_');

            const signatureInput = `${encodedHeader}.${encodedPayload}`;
            const signature = await signRS256(signatureInput, serviceAccount.private_key);

            const jwt = `${signatureInput}.${signature}`;

            // Exchange JWT for access token
            const tokenResponse = await fetch('https://oauth2.googleapis.com/token', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded'
                },
                body: `grant_type=urn:ietf:params:oauth:grant-type:jwt-bearer&assertion=${jwt}`
            });

            if (!tokenResponse.ok) {
                const errorText = await tokenResponse.text();
                throw new Error(`Token exchange failed: ${tokenResponse.status} ${tokenResponse.statusText} - ${errorText}`);
            }

            const tokenData = await tokenResponse.json();
            return tokenData.access_token;
        }

        // Sign data with RS256
        async function signRS256(data, privateKey) {
            const pemKey = privateKey.replace(/\\n/g, '\n');
            const keyData = pemKey.replace('-----BEGIN PRIVATE KEY-----', '').replace('-----END PRIVATE KEY-----', '').replace(/\s/g, '');
            const keyBuffer = Uint8Array.from(atob(keyData), c => c.charCodeAt(0));

            const cryptoKey = await crypto.subtle.importKey(
                'pkcs8',
                keyBuffer,
                {
                    name: 'RSASSA-PKCS1-v1_5',
                    hash: 'SHA-256'
                },
                false,
                ['sign']
            );

            const encoder = new TextEncoder();
            const dataBuffer = encoder.encode(data);
            const signature = await crypto.subtle.sign('RSASSA-PKCS1-v1_5', cryptoKey, dataBuffer);

            return btoa(String.fromCharCode(...new Uint8Array(signature)))
                .replace(/=/g, '')
                .replace(/\+/g, '-')
                .replace(/\//g, '_');
        }

        // Update progress bar
        function updateProgress(percentage) {
            document.getElementById('progressBar').style.width = percentage + '%';
            document.getElementById('progressBar').textContent = Math.round(percentage) + '%';
        }

        // Show upload results
        function showUploadResults(results) {
            const resultDiv = document.getElementById('result');
            const successCount = results.filter(r => r.success).length;
            const errorCount = results.filter(r => !r.success).length;

            let html = `<h4>📊 Upload Results</h4>`;
            html += `<p><strong>✅ Successful:</strong> ${successCount} | <strong>❌ Failed:</strong> ${errorCount}</p>`;

            if (results.length > 0) {
                html += '<ul>';
                results.forEach(result => {
                    if (result.success) {
                        const bucketName = document.getElementById('bucketName').value.trim();
                        const downloadUrl = `https://storage.cloud.google.com/${bucketName}/${result.result.name}`;
                        const publicUrl = `https://storage.googleapis.com/${bucketName}/${result.result.name}`;

                        html += `<li>
                            <strong>✅ ${result.file}</strong><br>
                            Size: ${formatFileSize(result.result.size)}<br>
                            <a href="${downloadUrl}" target="_blank">📥 Download</a> |
                            <a href="${publicUrl}" target="_blank">🔗 Public URL</a>
                        </li>`;
                    } else {
                        html += `<li class="error"><strong>❌ ${result.file}</strong><br>Error: ${result.error}</li>`;
                    }
                });
                html += '</ul>';
            }

            resultDiv.innerHTML = html;
            resultDiv.className = successCount > 0 ? 'result success' : 'result error';
            resultDiv.style.display = 'block';

            // Hide progress bar
            document.getElementById('progressContainer').style.display = 'none';
        }

        // Show result message
        function showResult(message, type) {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = `<h4>${type === 'success' ? '✅' : '❌'} ${message}</h4>`;
            resultDiv.className = `result ${type}`;
            resultDiv.style.display = 'block';
        }

        // Format file size
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // Load and display database contents
        async function loadAndDisplayDatabase() {
            const databaseView = document.getElementById('databaseView');
            const databaseContent = document.getElementById('databaseContent');

            try {
                // First try to load from the CSV file
                let csvContent = '';
                try {
                    const response = await fetch(CSV_DATABASE_PATH);
                    if (response.ok) {
                        csvContent = await response.text();
                    }
                } catch (error) {
                    console.log('Could not load CSV file, trying localStorage');
                }

                // Fallback to localStorage if CSV file not accessible
                if (!csvContent) {
                    csvContent = localStorage.getItem('gcs_database') || '';
                }

                if (!csvContent) {
                    databaseContent.innerHTML = '<p>📭 No database records found. Upload some files first!</p>';
                    databaseView.style.display = 'block';
                    return;
                }

                const lines = csvContent.split('\n').filter(line => line.trim());

                if (lines.length <= 1) {
                    databaseContent.innerHTML = '<p>📭 Database is empty. Upload some files first!</p>';
                    databaseView.style.display = 'block';
                    return;
                }

                // Parse CSV and create table
                const headers = parseCSVLine(lines[0]);
                let tableHtml = '<table class="database-table"><thead><tr>';

                // Create table headers (skip ID column for cleaner display)
                const displayHeaders = ['Original File', 'Upload Date', 'File Size', 'Actions'];
                displayHeaders.forEach(header => {
                    tableHtml += `<th>${header}</th>`;
                });
                tableHtml += '</tr></thead><tbody>';

                // Create table rows
                for (let i = 1; i < lines.length; i++) {
                    const columns = parseCSVLine(lines[i]);
                    if (columns.length >= 9) {
                        const originalFileName = columns[1].replace(/"/g, '');
                        const uploadDate = new Date(columns[6].replace(/"/g, '')).toLocaleString();
                        const fileSize = columns[5].replace(/"/g, '');
                        const downloadUrl = columns[7].replace(/"/g, '');
                        const publicUrl = columns[8].replace(/"/g, '');

                        tableHtml += `<tr>
                            <td><strong>${originalFileName}</strong></td>
                            <td>${uploadDate}</td>
                            <td>${fileSize}</td>
                            <td>
                                <a href="${downloadUrl}" target="_blank" class="download-link">📥 Download</a>
                                <a href="${publicUrl}" target="_blank" class="public-link">🔗 Public URL</a>
                            </td>
                        </tr>`;
                    }
                }

                tableHtml += '</tbody></table>';

                const recordCount = lines.length - 1;
                databaseContent.innerHTML = `
                    <p><strong>Total Records:</strong> ${recordCount}</p>
                    ${tableHtml}
                `;

                databaseView.style.display = 'block';

            } catch (error) {
                databaseContent.innerHTML = `<p>❌ Error loading database: ${error.message}</p>`;
                databaseView.style.display = 'block';
            }
        }

        // Close database view
        function closeDatabaseView() {
            document.getElementById('databaseView').style.display = 'none';
        }

        // Parse CSV line (simple parser)
        function parseCSVLine(line) {
            const result = [];
            let current = '';
            let inQuotes = false;

            for (let i = 0; i < line.length; i++) {
                const char = line[i];

                if (char === '"') {
                    inQuotes = !inQuotes;
                } else if (char === ',' && !inQuotes) {
                    result.push(current);
                    current = '';
                } else {
                    current += char;
                }
            }
            result.push(current);
            return result;
        }

        // Clear results when configuration changes
        ['projectId', 'bucketName', 'serviceAccount'].forEach(id => {
            document.getElementById(id).addEventListener('input', () => {
                document.getElementById('result').style.display = 'none';
            });
        });
    </script>
</body>
</html>
